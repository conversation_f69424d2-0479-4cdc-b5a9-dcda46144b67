# Langfuse Integration Setup Guide

This guide explains how to set up Langfuse observability for your SQL Evaluator application.

## What is Langfuse?

Langfuse is an open-source LLM engineering platform that provides:
- **Observability**: Track all LLM calls, inputs, outputs, and metadata
- **Analytics**: Monitor performance, costs, and usage patterns
- **Debugging**: Trace complex LLM workflows and identify issues
- **Evaluation**: Score and compare different model outputs
- **Cost Tracking**: Monitor API usage and costs across different providers

## Setup Instructions

### 1. Create a Langfuse Account

1. Go to [https://cloud.langfuse.com](https://cloud.langfuse.com)
2. Sign up for a free account
3. Create a new project for your SQL Evaluator

### 2. Get Your API Keys

1. In your Langfuse project dashboard, go to **Settings** → **API Keys**
2. Copy your **Public Key** (starts with `pk-lf-`)
3. Copy your **Secret Key** (starts with `sk-lf-`)

### 3. Configure Environment Variables

Create a `.env` file in your project root (copy from `.env.example`):

```bash
# Langfuse Configuration
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
```

### 4. Install Dependencies

The Langfuse dependency has already been added to your `pyproject.toml`. Install it with:

```bash
poetry install
```

### 5. Enable Langfuse in Configuration

In your `config.yaml`, ensure Langfuse is enabled:

```yaml
langfuse:
  enabled: true
  # Keys will be read from environment variables
  public_key: ""  # Leave empty to use LANGFUSE_PUBLIC_KEY
  secret_key: ""  # Leave empty to use LANGFUSE_SECRET_KEY
  host: "https://cloud.langfuse.com"
  session_id: "sql-evaluator-session"
  user_id: "sql-evaluator-user"
```

## What Gets Tracked

Once configured, Langfuse will automatically track:

### 1. SQL Generation (Performer)
- **Input**: User prompt and database schema
- **Output**: Generated SQL query
- **Metadata**: Model name, execution time, temperature settings
- **Costs**: Token usage and API costs (if available)

### 2. SQL Evaluation (Judge)
- **Input**: User prompt, generated SQL, and database schema
- **Output**: Evaluation scores and feedback
- **Metadata**: Model name, execution time, evaluation criteria
- **Scores**: Overall score, syntax score, logic score, etc.

### 3. Complete Evaluation Traces
- **End-to-end traces** linking performer and judge operations
- **Total execution times** for complete evaluations
- **Success/failure rates** and error tracking

## Using the Langfuse Dashboard

### 1. View Traces
- Go to your Langfuse project dashboard
- Click on **Traces** to see all SQL evaluation sessions
- Click on individual traces to see detailed breakdowns

### 2. Monitor Performance
- **Analytics** tab shows usage patterns and performance metrics
- **Models** tab compares different LLM performance
- **Users** tab tracks usage by different users/sessions

### 3. Debug Issues
- Filter traces by success/failure status
- View detailed error messages and stack traces
- Analyze slow-performing queries

### 4. Cost Tracking
- Monitor API costs across different LLM providers
- Track token usage patterns
- Set up cost alerts and budgets

## Advanced Configuration

### Custom Session and User Tracking

You can customize how sessions and users are tracked by modifying the configuration:

```yaml
langfuse:
  enabled: true
  session_id: "custom-session-id"
  user_id: "custom-user-id"
```

### Self-Hosted Langfuse

If you're running your own Langfuse instance:

```yaml
langfuse:
  enabled: true
  host: "https://your-langfuse-instance.com"
```

### Disable Langfuse

To disable Langfuse tracking:

```yaml
langfuse:
  enabled: false
```

## Troubleshooting

### Common Issues

1. **"Langfuse not initialized"**
   - Check that your API keys are correctly set in environment variables
   - Verify that `langfuse.enabled` is `true` in config.yaml

2. **"Failed to log metrics to Langfuse"**
   - Check your internet connection
   - Verify your Langfuse host URL is correct
   - Check the Langfuse service status

3. **Missing traces in dashboard**
   - Traces may take a few seconds to appear
   - Check that your project keys match your dashboard project
   - Ensure the application is properly flushing data on shutdown

### Debug Mode

To see detailed Langfuse logging, set the log level to DEBUG in your application.

## Benefits for Your SQL Evaluator

With Langfuse integrated, you can:

1. **Track Model Performance**: Compare how different LLMs perform on SQL generation and evaluation tasks
2. **Monitor Costs**: Keep track of API usage costs across different providers
3. **Debug Issues**: Quickly identify and fix problems with SQL generation or evaluation
4. **Analyze Usage Patterns**: Understand how users interact with your system
5. **Improve Over Time**: Use historical data to optimize prompts and model selection

## Next Steps

1. Set up your Langfuse account and get your API keys
2. Configure your environment variables
3. Run your SQL Evaluator application
4. Check your Langfuse dashboard to see traces appearing
5. Explore the analytics and insights available in Langfuse

For more advanced features and configuration options, check the [Langfuse documentation](https://langfuse.com/docs).
