#!/usr/bin/env python3
"""
Test script for Langfuse integration in SQL Evaluator.
This script tests the Langfuse integration without requiring a full database setup.
"""

import sys
from pathlib import Path

# Add root directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from langfuse_integration import initialize_langfuse, get_langfuse_client, flush_langfuse
from config_utils import load_full_config

def test_langfuse_initialization():
    """Test Langfuse initialization with current configuration."""
    print("🧪 Testing Langfuse Integration...")
    print("=" * 50)
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        config = load_full_config()
        print("✅ Configuration loaded successfully")
        
        # Test Langfuse initialization
        print("\n🔧 Initializing Langfuse...")
        success = initialize_langfuse(config)
        
        if success:
            print("✅ Langfuse initialized successfully!")
            
            # Test client access
            client = get_langfuse_client()
            if client:
                print("✅ Langfuse client is accessible")
                
                # Test a simple trace
                print("\n📊 Testing trace creation...")
                trace = client.trace(
                    name="test_trace",
                    metadata={
                        "test": True,
                        "component": "langfuse_integration_test"
                    }
                )
                print("✅ Test trace created successfully")
                
                # Test a simple generation
                print("📝 Testing generation logging...")
                generation = client.generation(
                    name="test_generation",
                    model="test-model",
                    input="Test input",
                    output="Test output",
                    metadata={
                        "test": True,
                        "execution_time": 0.1
                    }
                )
                print("✅ Test generation logged successfully")
                
                # Flush data
                print("\n🔄 Flushing test data...")
                flush_langfuse()
                print("✅ Data flushed successfully")
                
                print("\n🎉 All Langfuse integration tests passed!")
                print("\n📊 Check your Langfuse dashboard to see the test traces:")
                langfuse_config = config.get('langfuse', {})
                host = langfuse_config.get('host', 'https://cloud.langfuse.com')
                print(f"   {host}")
                
            else:
                print("❌ Langfuse client is not accessible")
                return False
                
        else:
            print("⚠️ Langfuse initialization failed or is disabled")
            print("\nPossible reasons:")
            print("1. Langfuse is disabled in config.yaml (langfuse.enabled: false)")
            print("2. Missing API keys (LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY)")
            print("3. Invalid API keys or host configuration")
            print("\nTo enable Langfuse:")
            print("1. Set environment variables:")
            print("   export LANGFUSE_PUBLIC_KEY='pk-lf-your-key'")
            print("   export LANGFUSE_SECRET_KEY='sk-lf-your-key'")
            print("2. Enable in config.yaml:")
            print("   langfuse:")
            print("     enabled: true")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_configuration_display():
    """Display current Langfuse configuration for debugging."""
    print("\n🔍 Current Langfuse Configuration:")
    print("=" * 40)
    
    try:
        config = load_full_config()
        langfuse_config = config.get('langfuse', {})
        
        print(f"Enabled: {langfuse_config.get('enabled', False)}")
        print(f"Host: {langfuse_config.get('host', 'Not set')}")
        print(f"Session ID: {langfuse_config.get('session_id', 'Not set')}")
        print(f"User ID: {langfuse_config.get('user_id', 'Not set')}")
        
        # Check environment variables (don't print actual keys for security)
        import os
        public_key = os.getenv('LANGFUSE_PUBLIC_KEY')
        secret_key = os.getenv('LANGFUSE_SECRET_KEY')
        
        print(f"Public Key (env): {'Set' if public_key else 'Not set'}")
        print(f"Secret Key (env): {'Set' if secret_key else 'Not set'}")
        
        if public_key:
            print(f"Public Key starts with: {public_key[:8]}...")
        if secret_key:
            print(f"Secret Key starts with: {secret_key[:8]}...")
            
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")

if __name__ == "__main__":
    print("🚀 SQL Evaluator - Langfuse Integration Test")
    print("=" * 60)
    
    # Display configuration
    test_configuration_display()
    
    # Run integration test
    success = test_langfuse_initialization()
    
    if success:
        print("\n✅ Integration test completed successfully!")
        print("Your Langfuse integration is working correctly.")
    else:
        print("\n❌ Integration test failed.")
        print("Please check the configuration and try again.")
        sys.exit(1)
