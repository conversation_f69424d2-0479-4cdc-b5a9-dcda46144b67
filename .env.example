# Environment Variables for SQL Evaluator

# Database Configuration
DB_NAME=proxym
DB_USER=postgres
DB_PASSWORD=DhiaAdmin
DB_HOST=localhost
DB_PORT=5432

# LLM API Keys (set the ones you need)
# Google Generative AI
GOOGLE_API_KEY=your_google_api_key_here

# OpenAI (if using OpenAI models)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic (if using Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Langfuse Configuration for LLM Observability
# Get these from your Langfuse project settings at https://cloud.langfuse.com
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com

# Optional: Custom session and user IDs for Langfuse tracking
LANGFUSE_SESSION_ID=sql-evaluator-session
LANGFUSE_USER_ID=sql-evaluator-user
