"""
Langfuse Integration Module for SQL Evaluator
Provides observability and analytics for LLM operations.
"""

import os
from typing import Optional, Dict, Any
from functools import wraps
from langfuse import Langfuse
from langfuse.decorators import observe, langfuse_context
from langfuse.callback import Callback<PERSON>andler
import logging

logger = logging.getLogger(__name__)

# Global Langfuse client
langfuse_client: Optional[Langfuse] = None
langfuse_handler: Optional[CallbackHandler] = None

def initialize_langfuse(config: Dict[str, Any]) -> bool:
    """
    Initialize Langfuse client with configuration.
    
    Args:
        config: Configuration dictionary containing Langfuse settings
        
    Returns:
        bool: True if initialization successful, False otherwise
    """
    global langfuse_client, langfuse_handler
    
    try:
        langfuse_config = config.get('langfuse', {})
        
        if not langfuse_config.get('enabled', False):
            logger.info("Langfuse is disabled in configuration")
            return False
        
        # Get credentials from environment variables or config
        public_key = os.getenv('LANGFUSE_PUBLIC_KEY') or langfuse_config.get('public_key')
        secret_key = os.getenv('LANGFUSE_SECRET_KEY') or langfuse_config.get('secret_key')
        host = os.getenv('LANGFUSE_HOST') or langfuse_config.get('host', 'https://cloud.langfuse.com')
        
        if not public_key or not secret_key:
            logger.warning("Langfuse credentials not found. Set LANGFUSE_PUBLIC_KEY and LANGFUSE_SECRET_KEY environment variables.")
            return False
        
        # Initialize Langfuse client
        langfuse_client = Langfuse(
            public_key=public_key,
            secret_key=secret_key,
            host=host
        )
        
        # Initialize callback handler for LangChain integration
        langfuse_handler = CallbackHandler(
            public_key=public_key,
            secret_key=secret_key,
            host=host,
            session_id=langfuse_config.get('session_id', 'sql-evaluator-session'),
            user_id=langfuse_config.get('user_id', 'sql-evaluator-user')
        )
        
        logger.info(f"✅ Langfuse initialized successfully with host: {host}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Langfuse: {e}")
        return False

def get_langfuse_handler() -> Optional[CallbackHandler]:
    """Get the Langfuse callback handler for LangChain integration."""
    return langfuse_handler

def get_langfuse_client() -> Optional[Langfuse]:
    """Get the Langfuse client for manual tracking."""
    return langfuse_client

def trace_sql_evaluation(func):
    """
    Decorator to trace SQL evaluation operations with Langfuse.
    
    This decorator will automatically track:
    - Input prompts and parameters
    - Generated SQL queries
    - Evaluation results
    - Execution times
    - Model information
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not langfuse_client:
            # If Langfuse is not initialized, just run the function normally
            return func(*args, **kwargs)
        
        try:
            # Extract relevant information for tracing
            function_name = func.__name__
            
            # Create a trace
            trace = langfuse_client.trace(
                name=f"sql_evaluation_{function_name}",
                metadata={
                    "function": function_name,
                    "component": "sql_evaluator"
                }
            )
            
            # Execute the function
            result = func(*args, **kwargs)
            
            # Update trace with results if it's a dict (typical for API responses)
            if isinstance(result, dict):
                trace.update(
                    output=result,
                    metadata={
                        "function": function_name,
                        "component": "sql_evaluator",
                        "success": result.get('success', False)
                    }
                )
            
            return result
            
        except Exception as e:
            if langfuse_client:
                trace.update(
                    metadata={
                        "function": function_name,
                        "component": "sql_evaluator",
                        "error": str(e)
                    }
                )
            raise
    
    return wrapper

def create_langchain_callbacks() -> list:
    """
    Create callback handlers for LangChain operations.
    
    Returns:
        list: List of callback handlers including Langfuse if available
    """
    callbacks = []
    
    if langfuse_handler:
        callbacks.append(langfuse_handler)
    
    return callbacks

def log_evaluation_metrics(
    user_prompt: str,
    generated_sql: str,
    evaluation_result: Dict[str, Any],
    performer_model: str,
    judge_model: str,
    execution_times: Dict[str, float]
):
    """
    Log detailed evaluation metrics to Langfuse.
    
    Args:
        user_prompt: The original user prompt
        generated_sql: The SQL query generated by the performer
        evaluation_result: The evaluation result from the judge
        performer_model: Name of the performer model
        judge_model: Name of the judge model
        execution_times: Dictionary with execution times
    """
    if not langfuse_client:
        return
    
    try:
        # Create a generation for the performer
        performer_generation = langfuse_client.generation(
            name="sql_generation",
            model=performer_model,
            input=user_prompt,
            output=generated_sql,
            metadata={
                "component": "performer",
                "execution_time": execution_times.get('performer', 0)
            }
        )
        
        # Create a generation for the judge
        judge_generation = langfuse_client.generation(
            name="sql_evaluation",
            model=judge_model,
            input={
                "user_prompt": user_prompt,
                "generated_sql": generated_sql
            },
            output=evaluation_result,
            metadata={
                "component": "judge",
                "execution_time": execution_times.get('judge', 0),
                "score": evaluation_result.get('overall_score', 0)
            }
        )
        
        # Create a trace that links both operations
        trace = langfuse_client.trace(
            name="complete_sql_evaluation",
            metadata={
                "total_execution_time": execution_times.get('total', 0),
                "performer_model": performer_model,
                "judge_model": judge_model,
                "overall_score": evaluation_result.get('overall_score', 0)
            }
        )
        
        logger.debug("✅ Evaluation metrics logged to Langfuse")
        
    except Exception as e:
        logger.error(f"❌ Failed to log metrics to Langfuse: {e}")

def flush_langfuse():
    """Flush any pending Langfuse data."""
    if langfuse_client:
        try:
            langfuse_client.flush()
        except Exception as e:
            logger.error(f"❌ Failed to flush Langfuse data: {e}")
